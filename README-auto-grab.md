# 自动抢单脚本使用说明

## 功能特点

✅ **循环获取订单** - 持续监控订单列表  
✅ **智能判断** - 根据返回值判断是否有可抢订单  
✅ **多次重试** - 每个订单多次尝试抢单，提高成功率  
✅ **自动退出** - 抢到订单后自动退出循环  
✅ **详细日志** - 完整记录抢单过程，便于调试  
✅ **优雅停止** - 支持 Ctrl+C 安全停止  

## 使用方法

### 方法1: 直接运行脚本

```bash
node auto-grab-script.js <uid> <token> [interval]
```

参数说明：
- `uid`: 用户ID（必需）
- `token`: 认证令牌（必需）  
- `interval`: 获取订单间隔时间，单位毫秒，默认5000（可选）

示例：
```bash
node auto-grab-script.js 12345 "your-token-here" 3000
```

### 方法2: 使用环境变量

```bash
export UID="12345"
export TOKEN="your-token-here"
export INTERVAL="3000"
node auto-grab-script.js
```

### 方法3: 通过服务器API

启动服务器：
```bash
node server/index.js
```

调用API：
```bash
# 启动自动抢单
curl -X POST http://localhost:3000/start-auto-grab \
  -H "Content-Type: application/json" \
  -d '{"uid":"12345","token":"your-token-here","interval":3000}'

# 查看状态
curl http://localhost:3000/auto-grab-status

# 停止抢单
curl -X POST http://localhost:3000/stop-auto-grab
```

## 工作流程

1. **获取订单列表** - 调用API获取当前可抢订单
2. **检查订单** - **仅通过data数组是否有订单来判断**，不依赖code、count等字段
3. **过滤订单** - **只保留game_area为"QQ"的订单，过滤掉"微信"订单**
4. **开始抢单** - 遍历过滤后的QQ订单，逐个尝试抢单
5. **重试机制** - 每个订单最多尝试5次
6. **成功退出** - 抢到任意一个订单后立即退出
7. **循环等待** - 如果没抢到，等待指定时间后重新获取订单

## 判断逻辑说明

### 📋 订单检测逻辑
⚠️ **重要**: 脚本只通过 `data` 数组是否包含订单来判断是否有可抢订单
- ✅ `data` 数组有订单 → 开始过滤和抢单
- ❌ `data` 数组为空 → 暂无订单，继续等待
- 🚫 不依赖 `code`、`count`、`message` 等其他字段

### 🎯 订单过滤逻辑
- ✅ **只抢取 `game_area` 为 "QQ" 的订单**
- 🚫 **过滤掉 `game_area` 为 "微信" 的订单**
- 📊 显示过滤前后的订单数量统计

### 🎯 抢单成功判断
- ✅ `code: 0` 且 `msg: "接单成功"` → 抢单成功，退出循环

### 🔄 抢单失败处理策略
- 🚪 `msg: "单子没接到"` → **退出抢单环节，回到获取订单步骤**
- 💔 `msg` 包含 "已被"、"不存在"、"已接" → 跳过当前订单，尝试下一个
- 🔄 其他情况（如 `msg: "再试一次"`）→ **立即重试，无延迟**

### ⏰ 延迟策略
- 🚫 **抢单重试无延迟** - 立即重试提高成功率
- ✅ **获取订单有延迟** - 避免频繁请求API

## 日志说明

- 🚀 开始自动抢单
- 📋 获取订单列表  
- 🎯 发现订单，开始抢单
- ⚡ 尝试抢单
- 🎉 抢单成功
- ❌ 抢单失败
- 💔 订单已被抢走
- 😴 暂无可抢订单
- ⏰ 等待下次获取
- 🏁 抢单结束

## 注意事项

1. **网络稳定性** - 确保网络连接稳定，避免请求失败
2. **合理间隔** - 建议设置3-5秒的获取间隔，避免请求过于频繁
3. **Token有效性** - 确保Token未过期，过期需要重新获取
4. **日志文件** - 程序会生成 `auto-grab.log` 日志文件
5. **安全停止** - 使用 Ctrl+C 安全停止程序

## 故障排除

### 常见错误

1. **401 Unauthorized** - Token过期或无效
2. **网络超时** - 检查网络连接
3. **订单已被抢走** - 正常现象，程序会自动尝试下一个订单

### 调试建议

1. 查看控制台日志输出
2. 检查 `auto-grab.log` 文件
3. 确认UID和Token的正确性
4. 适当调整获取间隔时间

## 配置建议

- **高频抢单**: interval = 2000-3000ms
- **正常抢单**: interval = 5000ms  
- **低频抢单**: interval = 10000ms

根据订单数量和竞争激烈程度调整间隔时间。
