// 测试商品ID过滤功能

// 模拟包含不同商品ID的订单响应
const testOrderResponse = {
  "code": 0,
  "count": 5,
  "data": [
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "允许的商品1",
      "goods_id": 51,  // 允许的商品ID
      "hj": null,
      "id": "12156164616",
      "pay_beizhu": "",
      "pay_texta": "老板拒绝联系",
      "pay_textb": "颜梦云",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12156164616",
      "update_time": "2025-06-05 16:57:41",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "微信",
      "gg_img": "",
      "gg_title": "微信区域订单",
      "goods_id": 86,  // 允许的商品ID，但区域是微信
      "hj": null,
      "id": "12157585800",
      "pay_beizhu": "",
      "pay_texta": "微信用户",
      "pay_textb": "测试用户",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585800",
      "update_time": "2025-06-06 00:36:01",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "不允许的商品",
      "goods_id": 73,  // 不允许的商品ID
      "hj": null,
      "id": "12157585801",
      "pay_beizhu": "",
      "pay_texta": "QQ用户",
      "pay_textb": "测试用户2",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585801",
      "update_time": "2025-06-06 00:37:01",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "允许的商品2",
      "goods_id": "84",  // 允许的商品ID（字符串格式）
      "hj": null,
      "id": "12157585802",
      "pay_beizhu": "",
      "pay_texta": "QQ用户",
      "pay_textb": "测试用户3",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585802",
      "update_time": "2025-06-06 00:38:01",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "允许的商品3",
      "goods_id": 52,  // 允许的商品ID
      "hj": null,
      "id": "12157585803",
      "pay_beizhu": "",
      "pay_texta": "QQ用户",
      "pay_textb": "测试用户4",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585803",
      "update_time": "2025-06-06 00:39:01",
      "user_id": null
    }
  ],
  "level": "info",
  "message": "获取到订单列表",
  "msg": "获取成功",
  "other": [],
  "server": "10.23.55.3",
  "timestamp": "2025-06-05T08:58:07.687Z"
};

// 测试商品ID过滤逻辑
function testGoodsIdFilter(orderResponse) {
  console.log('🧪 开始测试商品ID过滤功能\n');
  
  // 只通过data数组是否有订单来判断是否有可抢订单
  const orders = (orderResponse && Array.isArray(orderResponse.data)) ? orderResponse.data : [];
  
  console.log(`📋 原始订单数量: ${orders.length}`);
  
  if (orders.length > 0) {
    console.log(`🎯 发现 ${orders.length} 个订单，开始过滤和抢单`);
    
    // 打印所有原始订单
    console.log('\n📋 所有原始订单:');
    orders.forEach((order, index) => {
      console.log(`订单 ${index + 1}:`, {
        id: order.id,
        title: order.gg_title,
        area: order.game_area,
        goods_id: order.goods_id,
        updateTime: order.update_time
      });
    });
    
    // 允许的商品ID列表
    const allowedGoodsIds = ["51", "86", "84", "85", "52"];
    
    // 过滤订单：只保留 game_area 为 "QQ" 且 goods_id 在允许列表中的订单
    const filteredOrders = orders.filter(order => {
      const area = order.game_area;
      const goodsId = String(order.goods_id); // 确保转换为字符串进行比较
      
      // 检查区域
      if (area !== "QQ") {
        console.log(`🚫 过滤掉订单 ${order.id}，区域: ${area}（只抢QQ区域订单）`);
        return false;
      }
      
      // 检查商品ID
      if (!allowedGoodsIds.includes(goodsId)) {
        console.log(`🚫 过滤掉订单 ${order.id}，商品ID: ${goodsId}（不在允许列表中）`);
        return false;
      }
      
      console.log(`✅ 订单 ${order.id} 通过过滤，区域: ${area}，商品ID: ${goodsId}`);
      return true;
    });
    
    console.log(`\n✅ 过滤后剩余 ${filteredOrders.length} 个符合条件的订单`);
    
    // 打印过滤后的订单详情
    if (filteredOrders.length > 0) {
      console.log('\n📋 符合条件的订单:');
      filteredOrders.forEach((order, index) => {
        console.log(`符合条件的订单 ${index + 1}:`, {
          id: order.id,
          title: order.gg_title,
          area: order.game_area,
          goods_id: order.goods_id,
          updateTime: order.update_time
        });
      });
      
      console.log(`\n🎯 将对这 ${filteredOrders.length} 个订单进行抢单`);
    } else {
      console.log('\n😴 过滤后暂无符合条件的订单可抢（需要QQ区域且商品ID为51/86/84/85/52）');
    }
    
    return {
      totalOrders: orders.length,
      filteredOrders: filteredOrders.length,
      validOrders: filteredOrders,
      shouldGrab: filteredOrders.length > 0,
      filterStats: {
        areaFiltered: orders.filter(o => o.game_area !== 'QQ').length,
        goodsIdFiltered: orders.filter(o => o.game_area === 'QQ' && !allowedGoodsIds.includes(String(o.goods_id))).length
      }
    };
  } else {
    console.log('\n😴 暂无可抢订单');
    return {
      totalOrders: 0,
      filteredOrders: 0,
      validOrders: [],
      shouldGrab: false,
      filterStats: { areaFiltered: 0, goodsIdFiltered: 0 }
    };
  }
}

// 运行测试
function runTest() {
  console.log('🔬 开始测试商品ID过滤功能\n');
  console.log('=' * 60);
  
  console.log('\n📋 测试配置:');
  console.log('允许的商品ID: ["51", "86", "84", "85", "52"]');
  console.log('允许的区域: QQ');
  console.log('过滤的区域: 微信');
  
  const result = testGoodsIdFilter(testOrderResponse);
  
  console.log('\n📊 测试结果统计:');
  console.log(`总订单数: ${result.totalOrders}`);
  console.log(`符合条件订单数: ${result.filteredOrders}`);
  console.log(`区域过滤数: ${result.filterStats.areaFiltered}`);
  console.log(`商品ID过滤数: ${result.filterStats.goodsIdFiltered}`);
  console.log(`是否应该抢单: ${result.shouldGrab ? '是' : '否'}`);
  
  console.log('\n✅ 测试完成');
  
  return result;
}

// 运行测试
if (require.main === module) {
  runTest();
}

module.exports = {
  testGoodsIdFilter,
  testOrderResponse
};
