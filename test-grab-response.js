// 测试抢单响应解析逻辑

// 模拟不同的抢单响应
const testResponses = [
  // 成功响应
  {
    name: "抢单成功",
    response: {
      "code": 0,
      "msg": "接单成功",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  // 失败响应示例
  {
    name: "再试一次",
    response: {
      "code": 2,
      "msg": "再试一次",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  {
    name: "单子没接到",
    response: {
      "code": 2,
      "msg": "单子没接到",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  {
    name: "订单已被抢走",
    response: {
      "code": 1,
      "msg": "订单已被其他人抢走",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  {
    name: "订单不存在",
    response: {
      "code": 1,
      "msg": "订单不存在",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  {
    name: "订单已接",
    response: {
      "code": 1,
      "msg": "订单已接",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  },
  {
    name: "网络错误",
    response: {
      "code": 500,
      "msg": "服务器内部错误",
      "data": [],
      "count": 0,
      "other": [],
      "server": "10.23.55.8"
    }
  }
];

// 抢单响应解析函数
function parseGrabResponse(grabResponse, orderId, grabAttempts) {
  console.log(`\n🔍 解析抢单响应 - 订单ID: ${orderId}, 第${grabAttempts}次尝试`);
  console.log('响应数据:', JSON.stringify(grabResponse, null, 2));
  
  // 检查抢单是否成功通过msg来判断
  // 成功示例: {"code":0,"msg":"接单成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
  if (grabResponse && grabResponse.code === 0 && grabResponse.msg === "接单成功") {
    console.log(`🎉 抢单成功！订单ID: ${orderId}`);
    return {
      success: true,
      shouldExit: true,
      shouldBreak: false,
      message: "抢单成功"
    };
  } else {
    console.log(`❌ 抢单失败，第 ${grabAttempts} 次尝试`, {
      orderId,
      code: grabResponse?.code,
      msg: grabResponse?.msg,
      message: grabResponse?.message
    });
    
    // 根据msg内容决定处理策略
    if (grabResponse && grabResponse.msg === "单子没接到") {
      console.log(`💔 订单 ${orderId} 单子没接到，应该退出抢单环节，重新获取订单`);
      return {
        success: false,
        shouldExit: false,
        shouldBreak: true,
        message: "单子没接到，回到获取订单步骤"
      };
    } else if (grabResponse && (
      grabResponse.msg?.includes("已被") ||
      grabResponse.msg?.includes("不存在") ||
      grabResponse.msg?.includes("已接") ||
      grabResponse.message?.includes("已被") ||
      grabResponse.message?.includes("不存在") ||
      grabResponse.message?.includes("已接")
    )) {
      console.log(`💔 订单 ${orderId} 已被其他人抢走，应该尝试下一个订单`);
      return {
        success: false,
        shouldExit: false,
        shouldBreak: true,
        message: "订单已被抢走"
      };
    }

    console.log(`🔄 其他情况（如"再试一次"），继续重试，无需延迟`);
    return {
      success: false,
      shouldExit: false,
      shouldBreak: false,
      message: "可重试的错误，无需延迟"
    };
  }
}

// 测试所有响应
function testAllResponses() {
  console.log('🧪 开始测试抢单响应解析逻辑\n');
  
  testResponses.forEach((test, index) => {
    console.log(`\n📋 测试 ${index + 1}: ${test.name}`);
    console.log('=' * 50);
    
    const result = parseGrabResponse(test.response, "12345678", 1);
    
    console.log('解析结果:');
    console.log(`  - 成功: ${result.success}`);
    console.log(`  - 应该退出循环: ${result.shouldExit}`);
    console.log(`  - 应该跳过当前订单: ${result.shouldBreak}`);
    console.log(`  - 消息: ${result.message}`);
    
    // 根据结果给出建议
    if (result.shouldExit) {
      console.log('🎯 建议: 抢单成功，退出整个循环');
    } else if (result.shouldBreak) {
      console.log('🎯 建议: 订单已被抢走，尝试下一个订单');
    } else {
      console.log('🎯 建议: 可以重试当前订单');
    }
  });
  
  console.log('\n✅ 测试完成');
}

// 运行测试
if (require.main === module) {
  testAllResponses();
}

module.exports = {
  parseGrabResponse,
  testResponses
};
