# 故障排除指南

## 🚨 React异步组件错误

### 错误信息
```
Uncaught Error: An unknown Component is an async Client Component. 
Only Server Components can be async at the moment.
```

### 问题原因
这个错误通常由以下原因引起：
1. React组件中使用了异步函数但没有正确处理
2. useEffect中的异步调用没有正确包装
3. useCallback依赖项循环引用

### 解决方案

#### 方案1: 使用简化版本（推荐）
当前已切换到简化版本的App组件 (`SimpleApp.jsx`)，避免了复杂的异步状态管理。

要使用简化版本：
```bash
# 前端已自动切换到SimpleApp
# 直接启动即可
cd client/react-qd
npm run dev
```

#### 方案2: 修复完整版本
如果要使用完整功能版本，需要修复以下问题：

1. **修复useEffect中的异步调用**
```javascript
// 错误的方式
useEffect(() => {
  checkStatus(); // 直接调用异步函数
}, [checkStatus]);

// 正确的方式
useEffect(() => {
  checkStatus().catch(console.error);
}, [checkStatus]);
```

2. **修复useCallback依赖项**
```javascript
// 确保依赖项顺序正确，避免循环引用
const speak = useCallback((text) => {
  // 语音播报逻辑
}, []);

const startAutoGrab = useCallback(async () => {
  // 使用speak函数
}, [speak, uid, token, interval]);
```

#### 方案3: 切换回完整版本
要切换回完整功能版本：

1. 修改 `src/main.jsx`:
```javascript
import App from './App.jsx'  // 替换 SimpleApp

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <ErrorBoundary>
      <App />  {/* 替换 SimpleApp */}
    </ErrorBoundary>
  </StrictMode>,
)
```

2. 确保后端服务器运行:
```bash
node server/index.js
```

## 🔧 CORS错误解决

### 错误信息
```
Access to fetch at 'http://localhost:3000/...' from origin 'http://localhost:5173' 
has been blocked by CORS policy
```

### 解决方案
1. 确保后端服务器已启动
2. 检查server/index.js中的CORS配置：
```javascript
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
```

## 🚀 启动顺序

### 推荐启动顺序
1. **启动后端服务器**
```bash
cd server
node index.js
```

2. **启动前端开发服务器**
```bash
cd client/react-qd
npm run dev
```

3. **访问应用**
- 前端: http://localhost:5173
- 后端: http://localhost:3000

### 使用启动脚本
```bash
# 自动启动前后端
./start-dev.sh

# 仅启动前端（测试用）
./start-frontend-only.sh
```

## 📋 功能对比

### SimpleApp (当前使用)
✅ 基础UI界面  
✅ 参数输入和验证  
✅ 简单的状态管理  
✅ 日志显示  
❌ 后端API集成  
❌ 实时订单显示  
❌ 自动状态检查  

### 完整App (需要修复)
✅ 完整的后端API集成  
✅ 实时订单显示和过滤  
✅ 自动状态检查  
✅ 语音播报功能  
✅ 详细的运行统计  
⚠️ 需要解决异步组件问题  

## 🔍 调试建议

### 1. 检查浏览器控制台
- 查看具体的错误信息
- 检查网络请求状态
- 确认API响应

### 2. 检查后端服务
```bash
# 测试后端API
curl http://localhost:3000/auto-grab-status
```

### 3. 检查依赖安装
```bash
# 前端依赖
cd client/react-qd
npm install

# 后端依赖
cd server
npm install
```

## 📞 获取帮助

如果问题仍然存在：
1. 检查浏览器控制台的完整错误信息
2. 确认Node.js版本 (推荐 v16+)
3. 清除浏览器缓存
4. 重新安装依赖

### 快速重置
```bash
# 清理并重新安装
cd client/react-qd
rm -rf node_modules package-lock.json
npm install

cd ../../server
rm -rf node_modules package-lock.json
npm install
```
