const axios = require("axios");
const winston = require("winston");

// 配置日志记录器
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "auto-grab.log" })
  ],
});

// 获取订单
async function getOrderList(uid, token) {
  try {
    const response = await axios.request({
      method: "GET",
      url: "https://api.9100dianjing.cn/wxmini/my/partner/list/new",
      params: { uid, type: "" },
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639",
        "x-pwid": "9100",
        xweb_xhr: "1",
        authorization: `Bearer ${token}`,
        "content-type": "application/json",
        "sec-fetch-site": "cross-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        referer:
          "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
        "accept-language": "zh-CN,zh;q=0.9",
        priority: "u=1, i",
        Accept: "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        Connection: "keep-alive",
        "Cache-Control": "no-cache",
        Host: "api.9100dianjing.cn",
      },
    });
    return response.data;
  } catch (error) {
    logger.error("获取订单列表失败:", { error: error.message });
    throw error;
  }
}

// 抢单
async function grabOrder(orderId, token) {
  try {
    const response = await axios.request({
      method: "POST",
      url: "https://api.9100dianjing.cn/wxmini/my/partner/getorder",
      headers: {
        Referer:
          "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
        Authorization: `Bearer ${token}`,
        "X-Pwid": "9100",
        Host: "api.9100dianjing.cn",
        "User-Agent":
          "Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN",
        Accept: "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        Connection: "keep-alive",
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
      data: { order_id: orderId },
    });
    return response.data;
  } catch (error) {
    logger.error("抢单失败:", { error: error.message, orderId });
    throw error;
  }
}

// 自动抢单主函数
async function autoGrabOrders(uid, token, interval = 5000) {
  logger.info("🚀 开始自动抢单", { uid, interval });
  
  let isRunning = true;
  let totalAttempts = 0;
  
  // 监听退出信号
  process.on('SIGINT', () => {
    logger.info("收到退出信号，正在停止自动抢单...");
    isRunning = false;
  });
  
  while (isRunning) {
    try {
      totalAttempts++;
      logger.info(`📋 第 ${totalAttempts} 次获取订单列表...`);
      
      // 获取订单列表
      const orderResponse = await getOrderList(uid, token);
      
      // 只通过data数组是否有订单来判断是否有可抢订单
      // 不依赖code、count等其他字段
      const orders = (orderResponse && Array.isArray(orderResponse.data)) ? orderResponse.data : [];

      if (orders.length > 0) {
        logger.info(`🎯 发现 ${orders.length} 个订单，开始抢单`);

        // 打印订单详情
        orders.forEach((order, index) => {
          logger.info(`📋 订单 ${index + 1}:`, {
            id: order.id,
            title: order.gg_title,
            area: order.game_area,
            updateTime: order.update_time
          });
        });

        // 遍历所有订单，尝试抢单
        for (const order of orders) {
          if (!isRunning) break;

          const orderId = order.id;
          if (!orderId) {
            logger.warn("⚠️ 订单ID为空，跳过", { order });
            continue;
          }
            
            logger.info(`🎯 开始抢单: ${orderId}`);
            
            // 多次尝试抢单
            const maxGrabAttempts = 5;
            let grabAttempts = 0;
            let grabbed = false;
            
            while (grabAttempts < maxGrabAttempts && !grabbed && isRunning) {
              try {
                grabAttempts++;
                logger.info(`⚡ 第 ${grabAttempts} 次尝试抢单: ${orderId}`);
                
                const grabResponse = await grabOrder(orderId, token);
                
                // 检查抢单是否成功通过msg来判断
                // 成功示例: {"code":0,"msg":"接单成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
                if (grabResponse && grabResponse.code === 0 && grabResponse.msg === "接单成功") {
                  logger.info(`🎉 抢单成功！订单ID: ${orderId}`, { response: grabResponse });
                  grabbed = true;
                  isRunning = false; // 抢到订单后退出循环
                  return {
                    success: true,
                    orderId,
                    message: "抢单成功",
                    data: grabResponse
                  };
                } else {
                  logger.warn(`❌ 抢单失败，第 ${grabAttempts} 次尝试`, {
                    orderId,
                    code: grabResponse?.code,
                    msg: grabResponse?.msg,
                    message: grabResponse?.message
                  });

                  // 根据msg内容决定处理策略
                  if (grabResponse && grabResponse.msg === "单子没接到") {
                    logger.info(`💔 订单 ${orderId} 单子没接到，退出抢单环节，重新获取订单`);
                    break; // 跳出当前订单的抢单循环，回到获取订单步骤
                  } else if (grabResponse && (
                    grabResponse.msg?.includes("已被") ||
                    grabResponse.msg?.includes("不存在") ||
                    grabResponse.msg?.includes("已接") ||
                    grabResponse.message?.includes("已被") ||
                    grabResponse.message?.includes("不存在") ||
                    grabResponse.message?.includes("已接")
                  )) {
                    logger.info(`💔 订单 ${orderId} 已被其他人抢走，尝试下一个订单`);
                    break;
                  }

                  // 其他情况（如"再试一次"）继续重试，不需要延迟
                }
              } catch (error) {
                logger.error(`💥 抢单异常，第 ${grabAttempts} 次尝试`, {
                  orderId,
                  error: error.message
                });

                // 抢单异常时也不需要延迟，立即重试
              }
            }
            
            if (grabAttempts >= maxGrabAttempts && !grabbed) {
              logger.warn(`😞 订单 ${orderId} 抢单失败，已达到最大尝试次数`);
            }
          }
      } else {
        logger.info("😴 暂无可抢订单");
      }

      // 如果还在运行，等待指定间隔后继续
      if (isRunning) {
        logger.info(`⏰ 等待 ${interval}ms 后继续获取订单...`);
        await new Promise(resolve => setTimeout(resolve, interval));
      }

    } catch (error) {
      logger.error("💥 自动抢单过程中发生错误", { error: error.message });
      
      // 发生错误时也要等待一段时间再重试
      if (isRunning) {
        await new Promise(resolve => setTimeout(resolve, Math.min(interval, 10000)));
      }
    }
  }
  
  logger.info("🏁 自动抢单结束");
  return { success: false, message: "自动抢单结束" };
}

// 主程序入口
async function main() {
  // 从命令行参数或环境变量获取配置
  const uid = process.argv[2] || process.env.UID;
  const token = process.argv[3] || process.env.TOKEN;
  const interval = parseInt(process.argv[4] || process.env.INTERVAL || "5000");
  
  if (!uid || !token) {
    logger.error("❌ 缺少必要参数！");
    logger.info("使用方法: node auto-grab-script.js <uid> <token> [interval]");
    logger.info("或设置环境变量: UID, TOKEN, INTERVAL");
    process.exit(1);
  }
  
  logger.info("🔧 配置信息", { uid, interval });
  logger.info("按 Ctrl+C 停止自动抢单");
  
  try {
    const result = await autoGrabOrders(uid, token, interval);
    logger.info("✅ 程序结束", result);
  } catch (error) {
    logger.error("💥 程序异常结束", { error: error.message });
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  autoGrabOrders,
  getOrderList,
  grabOrder
};
