version: '3.8'

services:
  auto-grab-server:
    build: .
    container_name: auto-grab-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - auto-grab-network

networks:
  auto-grab-network:
    driver: bridge
