# Systemd服务文件
# 复制到 /etc/systemd/system/auto-grab-server.service
# 然后运行: sudo systemctl enable auto-grab-server

[Unit]
Description=Auto Grab Server
Documentation=https://github.com/your-repo/auto-grab-server
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/opt/auto-grab-server
ExecStart=/usr/bin/node index.js
Restart=on-failure
RestartSec=10
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
SyslogIdentifier=auto-grab-server

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=3000

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/auto-grab-server/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
