#!/bin/bash

# Docker部署脚本

set -e

echo "🐳 开始Docker部署..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose 未安装，请先安装 docker-compose${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs

# 停止现有容器（如果存在）
echo "🔄 停止现有容器..."
docker-compose down || true

# 构建并启动容器
echo "🏗️  构建Docker镜像..."
docker-compose build

echo "🚀 启动容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
else
    echo -e "${RED}❌ 服务启动失败，请检查日志${NC}"
    docker-compose logs
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Docker部署完成！${NC}"
echo ""
echo "📊 容器状态:"
docker-compose ps

echo ""
echo "📍 服务地址:"
echo "   API服务: http://localhost:3000"
echo "   健康检查: http://localhost:3000/health"
echo "   状态查询: http://localhost:3000/auto-grab-status"

echo ""
echo "🔧 常用命令:"
echo "   查看状态: docker-compose ps"
echo "   查看日志: docker-compose logs -f"
echo "   重启服务: docker-compose restart"
echo "   停止服务: docker-compose down"
echo "   进入容器: docker-compose exec auto-grab-server sh"

echo ""
echo -e "${YELLOW}⚠️  注意事项:${NC}"
echo "   1. 日志文件保存在 ./logs 目录"
echo "   2. 容器会自动重启"
echo "   3. 使用 docker-compose logs 查看详细日志"
