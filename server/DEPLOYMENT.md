# 自动抢单服务器部署指南

## 🎯 部署方式选择

### 方式1: PM2部署（推荐）
适合：VPS、云服务器、开发环境

### 方式2: Docker部署
适合：容器化环境、微服务架构

### 方式3: Systemd服务
适合：生产环境、系统级服务

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Node.js**: 16.x 或更高版本
- **内存**: 最少512MB，推荐1GB+
- **磁盘**: 最少1GB可用空间
- **网络**: 需要访问外部API

## 🚀 快速部署

### 方式1: PM2部署

```bash
# 1. 上传代码到服务器
scp -r server/ user@your-server:/opt/auto-grab-server/

# 2. 登录服务器
ssh user@your-server

# 3. 进入项目目录
cd /opt/auto-grab-server

# 4. 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

### 方式2: Docker部署

```bash
# 1. 上传代码到服务器
scp -r server/ user@your-server:/opt/auto-grab-server/

# 2. 登录服务器
ssh user@your-server

# 3. 进入项目目录
cd /opt/auto-grab-server

# 4. 运行Docker部署脚本
chmod +x deploy-docker.sh
./deploy-docker.sh
```

### 方式3: 手动部署

```bash
# 1. 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 创建用户和目录
sudo useradd -m -s /bin/bash nodejs
sudo mkdir -p /opt/auto-grab-server
sudo chown nodejs:nodejs /opt/auto-grab-server

# 3. 上传代码
scp -r server/* nodejs@your-server:/opt/auto-grab-server/

# 4. 安装依赖
cd /opt/auto-grab-server
npm ci --only=production

# 5. 配置systemd服务
sudo cp auto-grab-server.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable auto-grab-server
sudo systemctl start auto-grab-server
```

## 🔧 配置说明

### 环境变量

创建 `.env` 文件（可选）：
```bash
NODE_ENV=production
PORT=3000
LOG_LEVEL=info
```

### PM2配置

编辑 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'auto-grab-server',
    script: 'index.js',
    instances: 1,
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

### Docker配置

编辑 `docker-compose.yml`：
```yaml
services:
  auto-grab-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
```

## 🌐 反向代理配置

### Nginx配置

```bash
# 1. 安装Nginx
sudo apt update
sudo apt install nginx

# 2. 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/auto-grab-server

# 3. 启用站点
sudo ln -s /etc/nginx/sites-available/auto-grab-server /etc/nginx/sites-enabled/

# 4. 测试配置
sudo nginx -t

# 5. 重启Nginx
sudo systemctl restart nginx
```

### Apache配置

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
</VirtualHost>
```

## 🔒 安全配置

### 防火墙设置

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

### SSL证书（Let's Encrypt）

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 查看服务状态

```bash
# PM2
pm2 status
pm2 logs auto-grab-server

# Docker
docker-compose ps
docker-compose logs -f

# Systemd
sudo systemctl status auto-grab-server
sudo journalctl -u auto-grab-server -f
```

### 日志管理

```bash
# 日志轮转配置 /etc/logrotate.d/auto-grab-server
/opt/auto-grab-server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nodejs nodejs
    postrotate
        pm2 reload auto-grab-server
    endscript
}
```

## 🔄 更新部署

### PM2更新

```bash
cd /opt/auto-grab-server
git pull origin main  # 或上传新代码
npm ci --only=production
pm2 reload auto-grab-server
```

### Docker更新

```bash
cd /opt/auto-grab-server
git pull origin main  # 或上传新代码
docker-compose down
docker-compose build
docker-compose up -d
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :3000
sudo kill -9 <PID>
```

2. **权限问题**
```bash
sudo chown -R nodejs:nodejs /opt/auto-grab-server
sudo chmod +x /opt/auto-grab-server/index.js
```

3. **内存不足**
```bash
# 检查内存使用
free -h
# 添加swap
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 健康检查

```bash
# 检查服务是否运行
curl http://localhost:3000/health

# 检查API状态
curl http://localhost:3000/auto-grab-status
```

## 📞 技术支持

如果遇到部署问题：

1. 检查系统日志
2. 确认网络连接
3. 验证配置文件
4. 查看错误日志

部署成功后，服务将在 `http://your-server:3000` 可用。
