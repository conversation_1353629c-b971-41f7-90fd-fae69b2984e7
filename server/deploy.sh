#!/bin/bash

# 自动抢单服务器部署脚本

set -e

echo "🚀 开始部署自动抢单服务器..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}❌ 请不要使用root用户运行此脚本${NC}"
   exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js 18+${NC}"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo -e "${RED}❌ Node.js 版本过低，需要 16+ 版本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 版本检查通过: $(node -v)${NC}"

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p backups

# 备份现有服务（如果存在）
if pgrep -f "node.*index.js" > /dev/null; then
    echo "🔄 检测到运行中的服务，正在停止..."
    pkill -f "node.*index.js" || true
    sleep 2
fi

# 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装 PM2..."
    npm install -g pm2
fi

# 启动服务
echo "🚀 启动服务..."
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置PM2开机自启
pm2 startup

echo -e "${GREEN}✅ 部署完成！${NC}"
echo ""
echo "📊 服务状态:"
pm2 status

echo ""
echo "📍 服务地址:"
echo "   API服务: http://localhost:3000"
echo "   健康检查: http://localhost:3000/health"
echo "   状态查询: http://localhost:3000/auto-grab-status"

echo ""
echo "🔧 常用命令:"
echo "   查看状态: pm2 status"
echo "   查看日志: pm2 logs auto-grab-server"
echo "   重启服务: pm2 restart auto-grab-server"
echo "   停止服务: pm2 stop auto-grab-server"
echo "   删除服务: pm2 delete auto-grab-server"

echo ""
echo -e "${YELLOW}⚠️  注意事项:${NC}"
echo "   1. 确保防火墙开放3000端口"
echo "   2. 建议使用反向代理(nginx)进行生产部署"
echo "   3. 定期检查日志文件大小"
