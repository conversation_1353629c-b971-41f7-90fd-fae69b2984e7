// 测试订单过滤功能

// 模拟包含不同区域订单的响应
const testOrderResponse = {
  "code": 0,
  "count": 3,
  "data": [
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "新赛季保底单",
      "goods_id": 73,
      "hj": null,
      "id": "12156164616",
      "pay_beizhu": "",
      "pay_texta": "老板拒绝联系",
      "pay_textb": "颜梦云",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12156164616",
      "update_time": "2025-06-05 16:57:41",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "微信",
      "gg_img": "",
      "gg_title": "微信区域订单",
      "goods_id": 74,
      "hj": null,
      "id": "12157585800",
      "pay_beizhu": "",
      "pay_texta": "微信用户",
      "pay_textb": "测试用户",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585800",
      "update_time": "2025-06-06 00:36:01",
      "user_id": null
    },
    {
      "classify": 0,
      "g_title": "",
      "game_area": "QQ",
      "gg_img": "",
      "gg_title": "另一个QQ订单",
      "goods_id": 75,
      "hj": null,
      "id": "12157585801",
      "pay_beizhu": "",
      "pay_texta": "QQ用户",
      "pay_textb": "测试用户2",
      "service_zd": 0,
      "sl": null,
      "sytext": "订单编号：12157585801",
      "update_time": "2025-06-06 00:37:01",
      "user_id": null
    }
  ],
  "level": "info",
  "message": "获取到订单列表",
  "msg": "获取成功",
  "other": [],
  "server": "10.23.55.3",
  "timestamp": "2025-06-05T08:58:07.687Z"
};

// 测试订单过滤逻辑
function testOrderFilter(orderResponse) {
  console.log('🧪 开始测试订单过滤功能\n');
  
  // 只通过data数组是否有订单来判断是否有可抢订单
  const orders = (orderResponse && Array.isArray(orderResponse.data)) ? orderResponse.data : [];
  
  console.log(`📋 原始订单数量: ${orders.length}`);
  
  if (orders.length > 0) {
    console.log(`🎯 发现 ${orders.length} 个订单，开始过滤和抢单`);
    
    // 打印所有原始订单
    console.log('\n📋 所有原始订单:');
    orders.forEach((order, index) => {
      console.log(`订单 ${index + 1}:`, {
        id: order.id,
        title: order.gg_title,
        area: order.game_area,
        updateTime: order.update_time
      });
    });
    
    // 过滤订单：只保留 game_area 为 "QQ" 的订单
    const filteredOrders = orders.filter(order => {
      const area = order.game_area;
      if (area === "QQ") {
        return true;
      } else {
        console.log(`🚫 过滤掉订单 ${order.id}，区域: ${area}（只抢QQ区域订单）`);
        return false;
      }
    });
    
    console.log(`\n✅ 过滤后剩余 ${filteredOrders.length} 个QQ区域订单`);
    
    // 打印过滤后的订单详情
    if (filteredOrders.length > 0) {
      console.log('\n📋 过滤后的QQ订单:');
      filteredOrders.forEach((order, index) => {
        console.log(`QQ订单 ${index + 1}:`, {
          id: order.id,
          title: order.gg_title,
          area: order.game_area,
          updateTime: order.update_time
        });
      });
      
      console.log(`\n🎯 将对这 ${filteredOrders.length} 个QQ订单进行抢单`);
    } else {
      console.log('\n😴 过滤后暂无QQ区域订单可抢');
    }
    
    return {
      totalOrders: orders.length,
      filteredOrders: filteredOrders.length,
      qqOrders: filteredOrders,
      shouldGrab: filteredOrders.length > 0
    };
  } else {
    console.log('\n😴 暂无可抢订单');
    return {
      totalOrders: 0,
      filteredOrders: 0,
      qqOrders: [],
      shouldGrab: false
    };
  }
}

// 测试只有微信订单的情况
const wechatOnlyResponse = {
  "code": 0,
  "data": [
    {
      "game_area": "微信",
      "id": "12157585800",
      "gg_title": "微信区域订单",
      "update_time": "2025-06-06 00:36:01"
    }
  ]
};

// 测试只有QQ订单的情况
const qqOnlyResponse = {
  "code": 0,
  "data": [
    {
      "game_area": "QQ",
      "id": "12156164616",
      "gg_title": "QQ区域订单",
      "update_time": "2025-06-05 16:57:41"
    }
  ]
};

// 运行所有测试
function runAllTests() {
  console.log('🔬 开始全面测试订单过滤功能\n');
  console.log('=' * 60);
  
  // 测试1: 混合订单
  console.log('\n📋 测试1: 混合订单（QQ + 微信）');
  console.log('=' * 40);
  const result1 = testOrderFilter(testOrderResponse);
  console.log('测试结果:', result1);
  
  // 测试2: 只有微信订单
  console.log('\n📋 测试2: 只有微信订单');
  console.log('=' * 40);
  const result2 = testOrderFilter(wechatOnlyResponse);
  console.log('测试结果:', result2);
  
  // 测试3: 只有QQ订单
  console.log('\n📋 测试3: 只有QQ订单');
  console.log('=' * 40);
  const result3 = testOrderFilter(qqOnlyResponse);
  console.log('测试结果:', result3);
  
  // 测试4: 空订单
  console.log('\n📋 测试4: 空订单');
  console.log('=' * 40);
  const result4 = testOrderFilter({ code: 0, data: [] });
  console.log('测试结果:', result4);
  
  console.log('\n✅ 所有测试完成');
}

// 运行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testOrderFilter,
  testOrderResponse,
  wechatOnlyResponse,
  qqOnlyResponse
};
