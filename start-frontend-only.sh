#!/bin/bash

# 仅启动前端开发服务器（用于测试）

echo "🎨 启动前端开发服务器..."

# 检查是否在正确的目录
if [ ! -f "client/react-qd/package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 进入前端目录
cd client/react-qd

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端开发服务器
echo "🚀 启动前端开发服务器..."
npm run dev

echo "✅ 前端开发服务器已启动"
echo "📍 访问地址: http://localhost:5173"
echo "⚠️  注意: 后端服务器未启动，部分功能可能无法使用"
