#!/bin/bash

# 自动抢单系统开发环境启动脚本

echo "🚀 启动自动抢单系统开发环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "server/index.js" ] || [ ! -f "client/react-qd/package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 安装后端依赖
echo "📦 检查后端依赖..."
cd server
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 启动后端服务器
echo "🔧 启动后端服务器..."
node index.js &
BACKEND_PID=$!
echo "✅ 后端服务器已启动 (PID: $BACKEND_PID)"

# 等待后端启动
sleep 3

# 安装前端依赖
echo "📦 检查前端依赖..."
cd ../client/react-qd
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端开发服务器
echo "🎨 启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!
echo "✅ 前端开发服务器已启动 (PID: $FRONTEND_PID)"

# 等待服务启动
sleep 5

echo ""
echo "🎉 自动抢单系统已启动！"
echo ""
echo "📍 服务地址:"
echo "   前端界面: http://localhost:5173"
echo "   后端API:  http://localhost:3000"
echo ""
echo "📋 API接口:"
echo "   启动抢单: POST http://localhost:3000/start-auto-grab"
echo "   停止抢单: POST http://localhost:3000/stop-auto-grab"
echo "   查看状态: GET  http://localhost:3000/auto-grab-status"
echo ""
echo "🛑 停止服务: 按 Ctrl+C"
echo ""

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo '✅ 服务已停止'; exit 0" INT

# 保持脚本运行
wait
