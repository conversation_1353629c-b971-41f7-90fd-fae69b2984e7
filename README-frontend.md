# 自动抢单系统 - 前端界面使用说明

## 🎯 功能特点

✅ **可视化操作界面** - 直观的Web界面，便于操作  
✅ **实时状态监控** - 显示抢单运行状态和统计信息  
✅ **智能订单过滤** - 自动过滤微信订单，只抢QQ订单  
✅ **实时日志显示** - 完整的操作日志，便于调试  
✅ **订单实时预览** - 显示当前可抢订单和被过滤订单  
✅ **语音播报功能** - 可选的语音提醒功能  

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）

```bash
# 在项目根目录运行
./start-dev.sh
```

### 方法2: 手动启动

```bash
# 启动后端服务器
cd server
node index.js

# 新开终端，启动前端
cd client/react-qd
npm install  # 首次运行需要安装依赖
npm run dev
```

## 📱 界面功能说明

### 1. 基础配置区域

**用户ID输入框**
- 输入您的用户UID
- 运行时不可修改

**接单Token输入框**
- 输入您的认证Token
- 点击"验证"按钮可验证Token有效性
- 运行时不可修改

### 2. 设置选项

**抢单成功自动停止**
- 开启：抢到订单后自动停止
- 关闭：抢到订单后继续运行

**语音播报（测试功能）**
- 开启：重要事件会有语音提醒
- 关闭：静默运行

**获取订单间隔**
- 调节获取订单的时间间隔
- 范围：2-10秒
- 建议：3-5秒

### 3. 智能过滤说明

🎯 **只抢取QQ区域订单**  
🚫 **自动过滤微信区域订单**  
⚡ **抢单无延迟，快速重试**

### 4. 操作按钮

**开始抢单**
- 启动自动抢单功能
- 需要先输入UID和Token

**停止抢单**
- 停止当前运行的抢单任务

**重置**
- 清空输入的UID和Token
- 如果正在运行会先停止

### 5. 订单显示区域

**当前订单**
- 显示所有获取到的订单
- 绿色：可抢的QQ订单
- 橙色：被过滤的微信订单
- 自动刷新（运行时每10秒）

**统计信息**
- 总订单数
- QQ订单数（可抢）
- 微信订单数（已过滤）

### 6. 运行统计

**运行时长** - 当前任务运行时间  
**尝试次数** - 总的抢单尝试次数  
**成功次数** - 成功抢到的订单数量  
**过滤订单** - 被过滤掉的微信订单数量

### 7. 运行日志

**实时日志显示**
- 绿色：成功信息
- 红色：错误信息
- 灰色：普通信息

**日志类型**
- 系统启动/停止
- 订单获取情况
- 抢单尝试结果
- 过滤统计信息

## 🔧 使用流程

1. **启动系统**
   ```bash
   ./start-dev.sh
   ```

2. **打开浏览器**
   - 访问：http://localhost:5173

3. **配置参数**
   - 输入用户ID
   - 输入接单Token
   - 点击"验证"确认Token有效

4. **调整设置**
   - 设置获取订单间隔（建议3-5秒）
   - 选择是否开启语音播报
   - 选择是否自动停止

5. **开始抢单**
   - 点击"开始抢单"按钮
   - 观察日志和订单显示
   - 系统会自动过滤微信订单

6. **监控运行**
   - 查看实时日志
   - 观察订单过滤情况
   - 查看运行统计

7. **停止抢单**
   - 点击"停止抢单"按钮
   - 或等待自动停止（如果开启）

## 🎨 界面预览

```
┌─────────────────────────────────┐
│ 🟢 自动抢单运行中...              │
├─────────────────────────────────┤
│ 用户ID: [输入框]                 │
│ Token:  [输入框] [验证]          │
│ 自动停止: ○ 语音播报: ○          │
│ 间隔: ████████░░ 5秒             │
├─────────────────────────────────┤
│ [停止抢单] [重置]                │
├─────────────────────────────────┤
│ 📋 当前订单                      │
│ 总:3 QQ:2 微信:1                │
│ 🎯 QQ订单1 [可抢]               │
│ 🎯 QQ订单2 [可抢]               │
│ 🚫 微信订单 [已过滤]             │
├─────────────────────────────────┤
│ 📊 运行统计                      │
│ 时长:120秒 尝试:15次             │
│ 成功:1次 过滤:8次                │
├─────────────────────────────────┤
│ 📝 运行日志                      │
│ 14:30:15 ✅ 自动抢单已启动       │
│ 14:30:20 🎯 发现3个订单          │
│ 14:30:20 🚫 过滤微信订单         │
│ 14:30:25 ⚡ 尝试抢单...          │
└─────────────────────────────────┘
```

## 🔍 故障排除

### 常见问题

1. **前端无法访问**
   - 检查是否启动了前端服务
   - 确认端口5173未被占用

2. **后端连接失败**
   - 检查后端服务是否运行在3000端口
   - 确认防火墙设置

3. **Token验证失败**
   - 检查UID和Token格式
   - 确认Token未过期

4. **订单获取失败**
   - 检查网络连接
   - 确认Token权限

### 调试建议

1. 查看浏览器控制台错误信息
2. 查看后端服务器日志
3. 检查网络请求状态
4. 确认API接口可访问性

## 📞 技术支持

如果遇到问题，请检查：
1. 浏览器控制台日志
2. 后端服务器日志
3. 网络连接状态
4. Token有效性

系统会自动处理大部分错误情况，并在日志中显示详细信息。
