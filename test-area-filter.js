// 测试区域过滤功能

// 模拟包含不同区域和商品ID的订单响应
const testOrderResponse = {
  "code": 0,
  "count": 6,
  "data": [
    {
      "id": "12156164616",
      "gg_title": "QQ区域-允许商品",
      "game_area": "QQ",
      "goods_id": 51,
      "update_time": "2025-06-05 16:57:41"
    },
    {
      "id": "12157585800",
      "gg_title": "微信区域-允许商品",
      "game_area": "微信",
      "goods_id": 86,
      "update_time": "2025-06-06 00:36:01"
    },
    {
      "id": "12157585801",
      "gg_title": "QQ区域-不允许商品",
      "game_area": "QQ",
      "goods_id": 73,
      "update_time": "2025-06-06 00:37:01"
    },
    {
      "id": "12157585802",
      "gg_title": "QQ区域-允许商品2",
      "game_area": "QQ",
      "goods_id": 84,
      "update_time": "2025-06-06 00:38:01"
    },
    {
      "id": "12157585803",
      "gg_title": "微信区域-不允许商品",
      "game_area": "微信",
      "goods_id": 74,
      "update_time": "2025-06-06 00:39:01"
    },
    {
      "id": "12157585804",
      "gg_title": "其他区域-允许商品",
      "game_area": "其他",
      "goods_id": 52,
      "update_time": "2025-06-06 00:40:01"
    }
  ]
};

// 测试区域过滤逻辑
function testAreaFilter(orderResponse, allowedAreas = ["QQ"]) {
  console.log(`🧪 开始测试区域过滤功能 - 允许区域: [${allowedAreas.join(', ')}]\n`);
  
  const orders = (orderResponse && Array.isArray(orderResponse.data)) ? orderResponse.data : [];
  
  console.log(`📋 原始订单数量: ${orders.length}`);
  
  if (orders.length > 0) {
    console.log(`🎯 发现 ${orders.length} 个订单，开始过滤和抢单`);
    
    // 打印所有原始订单
    console.log('\n📋 所有原始订单:');
    orders.forEach((order, index) => {
      console.log(`订单 ${index + 1}:`, {
        id: order.id,
        title: order.gg_title,
        area: order.game_area,
        goods_id: order.goods_id
      });
    });
    
    // 允许的商品ID列表
    const allowedGoodsIds = ["51", "86", "84", "85", "52"];
    // 不允许的商品ID列表（黑名单）
    const blockedGoodsIds = ["73", "74", "88"];
    
    // 过滤订单：只保留指定区域且商品ID符合条件的订单
    const filteredOrders = orders.filter(order => {
      const area = order.game_area;
      const goodsId = String(order.goods_id);
      
      // 检查区域
      if (!allowedAreas.includes(area)) {
        console.log(`🚫 过滤掉订单 ${order.id}，区域: ${area}（只抢${allowedAreas.join('/')}区域订单）`);
        return false;
      }
      
      // 检查商品ID（黑名单）
      if (blockedGoodsIds.includes(goodsId)) {
        console.log(`🚫 过滤掉订单 ${order.id}，商品ID: ${goodsId}（在黑名单中）`);
        return false;
      }
      
      // 检查商品ID（白名单）
      if (!allowedGoodsIds.includes(goodsId)) {
        console.log(`🚫 过滤掉订单 ${order.id}，商品ID: ${goodsId}（不在白名单中）`);
        return false;
      }
      
      console.log(`✅ 订单 ${order.id} 通过过滤，区域: ${area}，商品ID: ${goodsId}`);
      return true;
    });
    
    console.log(`\n✅ 过滤后剩余 ${filteredOrders.length} 个符合条件的订单`);
    
    // 打印过滤后的订单详情
    if (filteredOrders.length > 0) {
      console.log('\n📋 符合条件的订单:');
      filteredOrders.forEach((order, index) => {
        console.log(`符合条件的订单 ${index + 1}:`, {
          id: order.id,
          title: order.gg_title,
          area: order.game_area,
          goods_id: order.goods_id
        });
      });
    } else {
      console.log(`\n😴 过滤后暂无符合条件的订单可抢（需要${allowedAreas.join('/')}区域且商品ID为51/86/84/85/52）`);
    }
    
    return {
      totalOrders: orders.length,
      filteredOrders: filteredOrders.length,
      validOrders: filteredOrders,
      filterStats: {
        areaFiltered: orders.filter(o => !allowedAreas.includes(o.game_area)).length,
        goodsIdFiltered: orders.filter(o => 
          allowedAreas.includes(o.game_area) && 
          (!allowedGoodsIds.includes(String(o.goods_id)) || blockedGoodsIds.includes(String(o.goods_id)))
        ).length
      }
    };
  } else {
    console.log('\n😴 暂无可抢订单');
    return {
      totalOrders: 0,
      filteredOrders: 0,
      validOrders: [],
      filterStats: { areaFiltered: 0, goodsIdFiltered: 0 }
    };
  }
}

// 运行多种测试场景
function runAllTests() {
  console.log('🔬 开始测试区域过滤功能\n');
  console.log('=' * 60);
  
  const testScenarios = [
    {
      name: "只允许QQ区域",
      allowedAreas: ["QQ"]
    },
    {
      name: "只允许微信区域",
      allowedAreas: ["微信"]
    },
    {
      name: "允许QQ和微信区域",
      allowedAreas: ["QQ", "微信"]
    },
    {
      name: "允许所有区域",
      allowedAreas: ["QQ", "微信", "其他"]
    }
  ];
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n📋 测试场景 ${index + 1}: ${scenario.name}`);
    console.log('=' * 40);
    
    const result = testAreaFilter(testOrderResponse, scenario.allowedAreas);
    
    console.log('\n📊 测试结果统计:');
    console.log(`总订单数: ${result.totalOrders}`);
    console.log(`符合条件订单数: ${result.filteredOrders}`);
    console.log(`区域过滤数: ${result.filterStats.areaFiltered}`);
    console.log(`商品ID过滤数: ${result.filterStats.goodsIdFiltered}`);
    console.log(`通过率: ${result.totalOrders > 0 ? (result.filteredOrders / result.totalOrders * 100).toFixed(1) : 0}%`);
  });
  
  console.log('\n✅ 所有测试完成');
}

// API调用示例
function showApiExample() {
  console.log('\n📡 API调用示例:');
  console.log('=' * 30);
  
  const examples = [
    {
      name: "只抢QQ区域订单",
      body: {
        uid: "12345",
        token: "your-token",
        interval: 5000,
        allowedAreas: ["QQ"]
      }
    },
    {
      name: "只抢微信区域订单",
      body: {
        uid: "12345",
        token: "your-token",
        interval: 5000,
        allowedAreas: ["微信"]
      }
    },
    {
      name: "抢QQ和微信区域订单",
      body: {
        uid: "12345",
        token: "your-token",
        interval: 5000,
        allowedAreas: ["QQ", "微信"]
      }
    }
  ];
  
  examples.forEach((example, index) => {
    console.log(`\n${index + 1}. ${example.name}:`);
    console.log('```bash');
    console.log('curl -X POST http://localhost:3000/start-auto-grab \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log(`  -d '${JSON.stringify(example.body, null, 2)}'`);
    console.log('```');
  });
  
  console.log('\n命令行示例:');
  console.log('```bash');
  console.log('# 只抢QQ区域');
  console.log('node auto-grab-script.js 12345 token123 5000 QQ');
  console.log('');
  console.log('# 抢QQ和微信区域');
  console.log('node auto-grab-script.js 12345 token123 5000 "QQ,微信"');
  console.log('```');
}

// 运行测试
if (require.main === module) {
  runAllTests();
  showApiExample();
}

module.exports = {
  testAreaFilter,
  testOrderResponse
};
