import { useState, useEffect, useCallback } from "react";
import autoGrabService from "../api/autoGrabService";

export const useAutoGrab = () => {
  // 基础状态
  const [isRunning, setIsRunning] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 用户输入
  const [uid, setUid] = useState("");
  const [token, setToken] = useState("");
  const [interval, setInterval] = useState(5000);

  // 设置选项
  const [autoStop, setAutoStop] = useState(true);
  const [voiceBroadcast, setVoiceBroadcast] = useState(false);

  // 日志和统计
  const [logs, setLogs] = useState([
    {
      time: new Date().toLocaleTimeString(),
      message: "系统初始化完成, 请设置参数后开始接单",
      type: "info",
    },
  ]);
  const [stats, setStats] = useState({
    totalAttempts: 0,
    successCount: 0,
    filteredCount: 0,
    startTime: null,
  });

  // 添加日志
  const addLog = useCallback((message, type = "info") => {
    const newLog = {
      time: new Date().toLocaleTimeString(),
      message,
      type,
    };
    setLogs((prev) => [...prev, newLog].slice(-50)); // 只保留最近50条日志
  }, []);

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 语音播报功能
  const speak = useCallback((text) => {
    if ("speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = "zh-CN";
      utterance.rate = 1;
      utterance.pitch = 1;
      window.speechSynthesis.speak(utterance);
    }
  }, []);

  // 检查运行状态
  const checkStatus = useCallback(async () => {
    try {
      const status = await autoGrabService.getAutoGrabStatus();
      setIsRunning(status.isRunning);
      return status;
    } catch (error) {
      console.error("检查状态失败:", error);
      return { isRunning: false };
    }
  }, []);

  // 启动自动抢单
  const startAutoGrab = useCallback(async () => {
    if (!uid || !token) {
      setError("请输入UID和Token");
      addLog("❌ 启动失败: 请输入UID和Token", "error");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      addLog("🚀 正在启动自动抢单...", "info");

      await autoGrabService.startAutoGrab(uid, token, interval);

      setIsRunning(true);
      setStats((prev) => ({
        ...prev,
        startTime: new Date(),
        totalAttempts: 0,
        successCount: 0,
        filteredCount: 0,
      }));

      addLog("✅ 自动抢单已启动", "success");
      addLog(`📊 配置: UID=${uid}, 间隔=${interval}ms`, "info");
      addLog("🎯 只抢取QQ区域订单，过滤微信区域订单", "info");

      // 语音播报
      if (voiceBroadcast) {
        speak("自动抢单已启动");
      }
    } catch (error) {
      setError(error.message);
      addLog(`❌ 启动失败: ${error.message}`, "error");
    } finally {
      setIsLoading(false);
    }
  }, [uid, token, interval, voiceBroadcast, addLog, speak]);

  // 停止自动抢单
  const stopAutoGrab = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      addLog("🛑 正在停止自动抢单...", "info");

      await autoGrabService.stopAutoGrab();

      setIsRunning(false);
      addLog("⏹️ 自动抢单已停止", "info");

      // 语音播报
      if (voiceBroadcast) {
        speak("自动抢单已停止");
      }
    } catch (error) {
      setError(error.message);
      addLog(`❌ 停止失败: ${error.message}`, "error");
    } finally {
      setIsLoading(false);
    }
  }, [voiceBroadcast, addLog, speak]);

  // 验证Token
  const validateToken = useCallback(async () => {
    if (!uid || !token) {
      setError("请输入UID和Token");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      addLog("🔍 正在验证Token...", "info");

      await autoGrabService.validateToken(uid, token);

      addLog("✅ Token验证成功", "success");
    } catch (error) {
      setError(error.message);
      addLog(`❌ Token验证失败: ${error.message}`, "error");
    } finally {
      setIsLoading(false);
    }
  }, [uid, token, addLog]);



  // 定期检查状态
  useEffect(() => {
    const interval = setInterval(checkStatus, 5000); // 每5秒检查一次状态
    return () => clearInterval(interval);
  }, [checkStatus]);

  // 初始化时检查状态
  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    // 状态
    isRunning,
    isLoading,
    error,
    logs,
    stats,

    // 用户输入
    uid,
    setUid,
    token,
    setToken,
    interval,
    setInterval,

    // 设置选项
    autoStop,
    setAutoStop,
    voiceBroadcast,
    setVoiceBroadcast,

    // 操作方法
    startAutoGrab,
    stopAutoGrab,
    validateToken,
    clearLogs,
    addLog,

    // 工具方法
    checkStatus,
  };
};
