// 自动抢单API服务

const API_BASE_URL = 'http://localhost:3000';

class AutoGrabService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 启动自动抢单
  async startAutoGrab(uid, token, interval = 5000) {
    try {
      const response = await fetch(`${this.baseURL}/start-auto-grab`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid,
          token,
          interval
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '启动失败');
      }

      return await response.json();
    } catch (error) {
      console.error('启动自动抢单失败:', error);
      throw error;
    }
  }

  // 停止自动抢单
  async stopAutoGrab() {
    try {
      const response = await fetch(`${this.baseURL}/stop-auto-grab`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '停止失败');
      }

      return await response.json();
    } catch (error) {
      console.error('停止自动抢单失败:', error);
      throw error;
    }
  }

  // 获取自动抢单状态
  async getAutoGrabStatus() {
    try {
      const response = await fetch(`${this.baseURL}/auto-grab-status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('获取状态失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取状态失败:', error);
      throw error;
    }
  }

  // 验证Token（可选功能）
  async validateToken(uid, token) {
    try {
      // 这里可以调用一个验证接口，暂时返回模拟结果
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
      
      // 简单验证：检查是否为空
      if (!uid || !token) {
        throw new Error('UID和Token不能为空');
      }
      
      if (uid.length < 3) {
        throw new Error('UID格式不正确');
      }
      
      if (token.length < 10) {
        throw new Error('Token格式不正确');
      }
      
      return {
        success: true,
        message: 'Token验证成功'
      };
    } catch (error) {
      console.error('Token验证失败:', error);
      throw error;
    }
  }

  // 获取订单列表（用于测试）
  async getOrderList(uid, token) {
    try {
      const response = await fetch(`https://api.9100dianjing.cn/wxmini/my/partner/list/new?uid=${uid}&type=`, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'x-pwid': '9100',
          'xweb_xhr': '1',
          'authorization': `Bearer ${token}`,
          'content-type': 'application/json',
          'Accept': '*/*',
        }
      });

      if (!response.ok) {
        throw new Error('获取订单列表失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取订单列表失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const autoGrabService = new AutoGrabService();

export default autoGrabService;
