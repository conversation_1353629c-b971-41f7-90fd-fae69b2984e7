import React, { useState, useEffect } from 'react';
import autoGrabService from '../api/autoGrabService';

const OrderDisplay = ({ uid, token, isRunning }) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // 获取订单列表
  const fetchOrders = async () => {
    if (!uid || !token) return;

    setLoading(true);
    setError(null);

    try {
      const response = await autoGrabService.getOrderList(uid, token);
      
      if (response && Array.isArray(response.data)) {
        const allOrders = response.data;
        
        // 过滤QQ订单
        const qqOrders = allOrders.filter(order => order.game_area === 'QQ');
        const wechatOrders = allOrders.filter(order => order.game_area === '微信');
        
        setOrders({
          all: allOrders,
          qq: qqOrders,
          wechat: wechatOrders
        });
        setLastUpdate(new Date());
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 定期刷新订单（仅在运行时）
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(fetchOrders, 10000); // 每10秒刷新一次
    fetchOrders(); // 立即执行一次

    return () => clearInterval(interval);
  }, [isRunning, uid, token]);

  if (!uid || !token) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow p-3 sm:p-4 mb-3 sm:mb-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm sm:text-base font-medium">当前订单</h3>
        <div className="flex items-center gap-2">
          {lastUpdate && (
            <span className="text-xs text-gray-500">
              {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={fetchOrders}
            disabled={loading}
            className="text-blue-500 text-xs sm:text-sm hover:text-blue-700 disabled:text-gray-400"
          >
            {loading ? '刷新中...' : '刷新'}
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-xs">
          获取订单失败: {error}
        </div>
      )}

      {orders.all && orders.all.length > 0 ? (
        <div className="space-y-3">
          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="bg-blue-50 p-2 rounded text-center">
              <div className="font-medium text-blue-700">{orders.all.length}</div>
              <div className="text-blue-600">总订单</div>
            </div>
            <div className="bg-green-50 p-2 rounded text-center">
              <div className="font-medium text-green-700">{orders.qq?.length || 0}</div>
              <div className="text-green-600">QQ订单</div>
            </div>
            <div className="bg-orange-50 p-2 rounded text-center">
              <div className="font-medium text-orange-700">{orders.wechat?.length || 0}</div>
              <div className="text-orange-600">微信订单</div>
            </div>
          </div>

          {/* QQ订单列表 */}
          {orders.qq && orders.qq.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-green-700 mb-2">
                🎯 可抢订单 (QQ区域)
              </h4>
              <div className="space-y-2">
                {orders.qq.map((order, index) => (
                  <div key={order.id} className="bg-green-50 border border-green-200 rounded p-2">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="text-sm font-medium">{order.gg_title}</div>
                        <div className="text-xs text-gray-600 mt-1">
                          ID: {order.id} | 区域: {order.game_area}
                        </div>
                        <div className="text-xs text-gray-500">
                          更新: {order.update_time}
                        </div>
                      </div>
                      <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        可抢
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 微信订单列表（被过滤） */}
          {orders.wechat && orders.wechat.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-orange-700 mb-2">
                🚫 已过滤订单 (微信区域)
              </h4>
              <div className="space-y-2">
                {orders.wechat.slice(0, 3).map((order, index) => (
                  <div key={order.id} className="bg-orange-50 border border-orange-200 rounded p-2 opacity-60">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="text-sm">{order.gg_title}</div>
                        <div className="text-xs text-gray-600 mt-1">
                          ID: {order.id} | 区域: {order.game_area}
                        </div>
                      </div>
                      <div className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                        已过滤
                      </div>
                    </div>
                  </div>
                ))}
                {orders.wechat.length > 3 && (
                  <div className="text-xs text-gray-500 text-center">
                    还有 {orders.wechat.length - 3} 个微信订单被过滤...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-4 text-gray-500 text-sm">
          {loading ? '正在获取订单...' : '暂无订单'}
        </div>
      )}
    </div>
  );
};

export default OrderDisplay;
