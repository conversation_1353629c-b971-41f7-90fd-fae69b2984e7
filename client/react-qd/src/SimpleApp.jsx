import React, { useState } from 'react';

function SimpleApp() {
  const [uid, setUid] = useState('');
  const [token, setToken] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState([
    { time: new Date().toLocaleTimeString(), message: '系统初始化完成', type: 'info' }
  ]);

  const addLog = (message, type = 'info') => {
    const newLog = {
      time: new Date().toLocaleTimeString(),
      message,
      type
    };
    setLogs(prev => [...prev, newLog].slice(-10)); // 只保留最近10条
  };

  const handleStart = () => {
    if (!uid || !token) {
      addLog('请输入UID和Token', 'error');
      return;
    }
    setIsRunning(true);
    addLog('自动抢单已启动', 'success');
  };

  const handleStop = () => {
    setIsRunning(false);
    addLog('自动抢单已停止', 'info');
  };

  return (
    <div className="max-w-md mx-auto bg-gray-50 min-h-screen p-4">
      <div className="bg-white rounded-lg shadow p-4 mb-4">
        <h2 className="text-lg font-medium mb-4">自动抢单系统</h2>
        
        {/* 状态指示器 */}
        {isRunning && (
          <div className="mb-4 p-2 bg-green-50 border border-green-200 rounded">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-700 text-sm">自动抢单运行中...</span>
            </div>
          </div>
        )}

        {/* 用户ID输入 */}
        <div className="mb-4">
          <label className="block text-sm mb-1">用户ID</label>
          <input
            type="text"
            placeholder="请输入用户UID"
            value={uid}
            onChange={(e) => setUid(e.target.value)}
            disabled={isRunning}
            className="w-full p-2 border border-gray-200 rounded text-sm disabled:bg-gray-100"
          />
        </div>

        {/* Token输入 */}
        <div className="mb-4">
          <label className="block text-sm mb-1">接单Token</label>
          <input
            type="text"
            placeholder="请输入接单Token"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            disabled={isRunning}
            className="w-full p-2 border border-gray-200 rounded text-sm disabled:bg-gray-100"
          />
        </div>

        {/* 功能说明 */}
        <div className="mb-4 p-2 bg-blue-50 border border-blue-200 rounded">
          <div className="text-sm text-blue-700">
            <div className="font-medium mb-1">🎯 智能过滤:</div>
            <div className="text-xs">
              • ✅ 只抢取QQ区域订单<br/>
              • 🚫 自动过滤微信区域订单<br/>
              • ⚡ 抢单无延迟，快速重试
            </div>
          </div>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        {!isRunning ? (
          <button
            onClick={handleStart}
            disabled={!uid || !token}
            className="bg-green-500 text-white py-3 rounded text-center disabled:bg-gray-300"
          >
            开始抢单
          </button>
        ) : (
          <button
            onClick={handleStop}
            className="bg-red-500 text-white py-3 rounded text-center"
          >
            停止抢单
          </button>
        )}
        <button
          onClick={() => {
            setUid('');
            setToken('');
            if (isRunning) handleStop();
          }}
          className="bg-white border border-gray-200 py-3 rounded text-center"
        >
          重置
        </button>
      </div>

      {/* 日志区域 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">运行日志</h3>
          <button
            onClick={() => setLogs([])}
            className="text-gray-400 text-sm hover:text-gray-600"
          >
            清空
          </button>
        </div>
        <div className="bg-gray-50 p-2 text-sm rounded max-h-48 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500">暂无日志</p>
          ) : (
            logs.map((log, index) => (
              <div
                key={index}
                className={`mb-1 ${
                  log.type === 'error' ? 'text-red-600' :
                  log.type === 'success' ? 'text-green-600' :
                  'text-gray-600'
                }`}
              >
                <span className="text-gray-400">{log.time}</span> {log.message}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 提示信息 */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <div className="text-yellow-800 text-sm">
          <div className="font-medium mb-1">⚠️ 注意事项:</div>
          <div className="text-xs">
            • 确保后端服务器已启动 (端口3000)<br/>
            • 输入正确的UID和Token<br/>
            • 系统会自动过滤微信订单
          </div>
        </div>
      </div>
    </div>
  );
}

export default SimpleApp;
