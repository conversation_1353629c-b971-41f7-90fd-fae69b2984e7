import React from 'react';
import { useAutoGrab } from './hooks/useAutoGrab';
import OrderDisplay from './components/OrderDisplay';

function App() {
  const {
    // 状态
    isRunning,
    isLoading,
    error,
    logs,
    stats,

    // 用户输入
    uid,
    setUid,
    token,
    setToken,
    interval,
    setInterval,

    // 设置选项
    autoStop,
    setAutoStop,
    voiceBroadcast,
    setVoiceBroadcast,

    // 操作方法
    startAutoGrab,
    stopAutoGrab,
    validateToken,
    clearLogs
  } = useAutoGrab();

  return (
    <div className="max-w-md mx-auto bg-gray-50 min-h-screen p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow p-3 sm:p-4 mb-3 sm:mb-4">
        {/* 标题和关闭按钮 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <h2 className="text-base sm:text-lg font-medium">接单设置</h2>
          <button className="text-blue-500 text-sm sm:text-base">关闭</button>
        </div>

        {/* 运行状态指示器 */}
        {isRunning && (
          <div className="mb-3 sm:mb-4 p-2 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-700 text-sm">自动抢单运行中...</span>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="mb-3 sm:mb-4 p-2 bg-red-50 border border-red-200 rounded-md">
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {/* 用户ID输入框 */}
        <div className="mb-3 sm:mb-4">
          <label className="block text-xs sm:text-sm mb-1">用户ID</label>
          <input
            type="text"
            placeholder="请输入用户Uid"
            value={uid}
            onChange={(e) => setUid(e.target.value)}
            disabled={isRunning}
            className="w-full p-2 sm:p-3 border border-gray-200 rounded-md text-sm disabled:bg-gray-100"
          />
        </div>

        {/* 接单Token输入框和验证按钮 */}
        <div className="mb-3 sm:mb-4">
          <label className="block text-xs sm:text-sm mb-1">接单Token</label>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="请输入接单Token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              disabled={isRunning}
              className="flex-grow p-2 sm:p-3 border border-gray-200 rounded-md text-sm disabled:bg-gray-100"
            />
            <button
              onClick={validateToken}
              disabled={isLoading || isRunning || !uid || !token}
              className="bg-blue-500 text-white px-3 sm:px-4 rounded-md text-sm whitespace-nowrap disabled:bg-gray-300"
            >
              {isLoading ? '验证中...' : '验证'}
            </button>
          </div>
        </div>

        {/* 抢单成功自动停止开关 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <span className="text-xs sm:text-sm">抢单成功自动停止</span>
          <div 
            className={`w-12 h-6 rounded-full p-1 cursor-pointer ${autoStop ? 'bg-blue-500' : 'bg-gray-200'}`}
            onClick={() => setAutoStop(!autoStop)}
          >
            <div 
              className={`bg-white w-4 h-4 rounded-full shadow transform ${autoStop ? 'translate-x-6' : 'translate-x-0'} transition`}
            />
          </div>
        </div>

        {/* 语音播报开关 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <div>
            <span className="text-xs sm:text-sm">语音播报</span>
            <span className="text-blue-500 text-xs sm:text-sm ml-1 sm:ml-2">(测试)</span>
          </div>
          <div 
            className={`w-12 h-6 rounded-full p-1 cursor-pointer ${voiceBroadcast ? 'bg-blue-500' : 'bg-gray-200'}`}
            onClick={() => setVoiceBroadcast(!voiceBroadcast)}
          >
            <div 
              className={`bg-white w-4 h-4 rounded-full shadow transform ${voiceBroadcast ? 'translate-x-6' : 'translate-x-0'} transition`}
            />
          </div>
        </div>

        {/* 刷新间隔滑块 */}
        <div className="mb-3 sm:mb-4">
          <div className="flex justify-between text-xs sm:text-sm mb-1">
            <span>获取订单间隔: {interval}ms</span>
            <span className="text-gray-500">{(interval/1000).toFixed(1)}秒/次</span>
          </div>
          <input
            type="range"
            min="2000"
            max="10000"
            step="500"
            value={interval}
            onChange={(e) => setInterval(parseInt(e.target.value))}
            disabled={isRunning}
            className="w-full h-2 bg-gray-200 rounded-md appearance-none cursor-pointer disabled:cursor-not-allowed"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>2秒</span>
            <span>10秒</span>
          </div>
        </div>

        {/* 订单过滤说明 */}
        <div className="mb-2 sm:mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
          <div className="text-xs sm:text-sm text-blue-700">
            <div className="flex items-center gap-1 mb-1">
              <span>🎯</span>
              <span className="font-medium">智能过滤:</span>
            </div>
            <div className="text-xs">
              • ✅ 只抢取QQ区域订单<br/>
              • 🚫 自动过滤微信区域订单<br/>
              • ⚡ 抢单无延迟，快速重试
            </div>
          </div>
        </div>
      </div>

      {/* 按钮区域 */}
      <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
        {!isRunning ? (
          <button
            onClick={startAutoGrab}
            disabled={isLoading || !uid || !token}
            className="bg-green-500 text-white py-2 sm:py-3 rounded-md text-center text-sm sm:text-base disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            {isLoading ? '启动中...' : '开始抢单'}
          </button>
        ) : (
          <button
            onClick={stopAutoGrab}
            disabled={isLoading}
            className="bg-red-500 text-white py-2 sm:py-3 rounded-md text-center text-sm sm:text-base disabled:bg-gray-300"
          >
            {isLoading ? '停止中...' : '停止抢单'}
          </button>
        )}
        <button
          onClick={() => {
            setUid('');
            setToken('');
            if (isRunning) stopAutoGrab();
          }}
          className="bg-white border border-gray-200 py-2 sm:py-3 rounded-md text-center text-sm sm:text-base"
        >
          重置
        </button>
      </div>

      {/* 订单显示 */}
      <OrderDisplay
        uid={uid}
        token={token}
        isRunning={isRunning}
      />

      {/* 统计信息 */}
      {stats.startTime && (
        <div className="bg-white rounded-lg shadow p-3 sm:p-4 mb-3 sm:mb-4">
          <h3 className="text-sm sm:text-base font-medium mb-2">运行统计</h3>
          <div className="grid grid-cols-2 gap-4 text-xs sm:text-sm">
            <div>
              <span className="text-gray-500">运行时长:</span>
              <span className="ml-1 font-medium">
                {Math.floor((new Date() - stats.startTime) / 1000)}秒
              </span>
            </div>
            <div>
              <span className="text-gray-500">尝试次数:</span>
              <span className="ml-1 font-medium">{stats.totalAttempts}</span>
            </div>
            <div>
              <span className="text-gray-500">成功次数:</span>
              <span className="ml-1 font-medium text-green-600">{stats.successCount}</span>
            </div>
            <div>
              <span className="text-gray-500">过滤订单:</span>
              <span className="ml-1 font-medium text-orange-600">{stats.filteredCount}</span>
            </div>
          </div>
        </div>
      )}

      {/* 运行日志区域 */}
      <div className="bg-white rounded-lg shadow p-3 sm:p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm sm:text-base font-medium">运行日志</h3>
          <button
            onClick={clearLogs}
            className="text-gray-400 text-xs sm:text-sm hover:text-gray-600"
          >
            清空
          </button>
        </div>
        <div className="bg-gray-50 p-2 sm:p-3 text-xs sm:text-sm rounded max-h-64 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500">暂无日志</p>
          ) : (
            logs.map((log, index) => (
              <div
                key={index}
                className={`mb-1 ${
                  log.type === 'error' ? 'text-red-600' :
                  log.type === 'success' ? 'text-green-600' :
                  'text-gray-600'
                }`}
              >
                <span className="text-gray-400">{log.time}</span> {log.message}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
