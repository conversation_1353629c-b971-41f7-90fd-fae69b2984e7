import React, { useState } from 'react';

function App() {
  const [autoStop, setAutoStop] = useState(false);
  const [voiceBroadcast, setVoiceBroadcast] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(1);
  const [grabDelay, setGrabDelay] = useState(0.2);

  return (
    <div className="max-w-md mx-auto bg-gray-50 min-h-screen p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow p-3 sm:p-4 mb-3 sm:mb-4">
        {/* 标题和关闭按钮 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <h2 className="text-base sm:text-lg font-medium">接单设置</h2>
          <button className="text-blue-500 text-sm sm:text-base">关闭</button>
        </div>

        {/* 用户ID输入框 */}
        <div className="mb-3 sm:mb-4">
          <label className="block text-xs sm:text-sm mb-1">用户ID</label>
          <input 
            type="text" 
            placeholder="请输入用户Uid" 
            className="w-full p-2 sm:p-3 border border-gray-200 rounded-md text-sm" 
          />
        </div>

        {/* 接单Token输入框和验证按钮 */}
        <div className="mb-3 sm:mb-4">
          <label className="block text-xs sm:text-sm mb-1">接单Token</label>
          <div className="flex gap-2">
            <input 
              type="text" 
              placeholder="请输入接单Token" 
              className="flex-grow p-2 sm:p-3 border border-gray-200 rounded-md text-sm" 
            />
            <button className="bg-blue-500 text-white px-3 sm:px-4 rounded-md text-sm whitespace-nowrap">验证</button>
          </div>
        </div>

        {/* 抢单成功自动停止开关 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <span className="text-xs sm:text-sm">抢单成功自动停止</span>
          <div 
            className={`w-12 h-6 rounded-full p-1 cursor-pointer ${autoStop ? 'bg-blue-500' : 'bg-gray-200'}`}
            onClick={() => setAutoStop(!autoStop)}
          >
            <div 
              className={`bg-white w-4 h-4 rounded-full shadow transform ${autoStop ? 'translate-x-6' : 'translate-x-0'} transition`}
            />
          </div>
        </div>

        {/* 语音播报开关 */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <div>
            <span className="text-xs sm:text-sm">语音播报</span>
            <span className="text-blue-500 text-xs sm:text-sm ml-1 sm:ml-2">(测试)</span>
          </div>
          <div 
            className={`w-12 h-6 rounded-full p-1 cursor-pointer ${voiceBroadcast ? 'bg-blue-500' : 'bg-gray-200'}`}
            onClick={() => setVoiceBroadcast(!voiceBroadcast)}
          >
            <div 
              className={`bg-white w-4 h-4 rounded-full shadow transform ${voiceBroadcast ? 'translate-x-6' : 'translate-x-0'} transition`}
            />
          </div>
        </div>

        {/* 刷新间隔滑块 */}
        <div className="mb-3 sm:mb-4">
          <div className="flex justify-between text-xs sm:text-sm mb-1">
            <span>刷新间隔 (毫秒): {refreshInterval} 秒/次</span>
          </div>
          <input 
            type="range" 
            min="0" 
            max="5" 
            step="0.1" 
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(parseFloat(e.target.value))} 
            className="w-full h-2 bg-gray-200 rounded-md appearance-none cursor-pointer" 
          />
        </div>

        {/* 抢单延迟滑块 */}
        <div className="mb-2 sm:mb-3">
          <div className="flex justify-between text-xs sm:text-sm mb-1">
            <span>抢单延迟 (毫秒): {grabDelay} 秒</span>
          </div>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1" 
            value={grabDelay}
            onChange={(e) => setGrabDelay(parseFloat(e.target.value))} 
            className="w-full h-2 bg-gray-200 rounded-md appearance-none cursor-pointer" 
          />
        </div>
      </div>

      {/* 按钮区域 */}
      <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
        <button className="bg-green-500 text-white py-2 sm:py-3 rounded-md text-center text-sm sm:text-base">开始</button>
        <button className="bg-white border border-gray-200 py-2 sm:py-3 rounded-md text-center text-sm sm:text-base">登出</button>
      </div>

      {/* 运行日志区域 */}
      <div className="bg-white rounded-lg shadow p-3 sm:p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm sm:text-base font-medium">运行日志</h3>
          <button className="text-gray-400 text-xs sm:text-sm">清空</button>
        </div>
        <div className="bg-gray-50 p-2 sm:p-3 text-xs sm:text-sm text-gray-600 rounded">
          <p>13:12:52 系统初始化完成, 请设置参数后开始接单</p>
        </div>
      </div>
    </div>
  );
}

export default App;
