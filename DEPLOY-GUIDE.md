# 🚀 自动抢单服务器部署指南

## 📦 打包步骤

### Linux/Mac 打包
```bash
chmod +x package-server.sh
./package-server.sh
```

### Windows 打包
```cmd
package-server.bat
```

打包完成后会生成 `auto-grab-server-YYYYMMDD-HHMMSS.tar.gz` 或对应的文件夹。

## 🌐 服务器部署

### 方式1: 一键部署（推荐）

**Linux服务器**：
```bash
# 1. 上传并解压
scp auto-grab-server-*.tar.gz user@your-server:~/
ssh user@your-server
tar -xzf auto-grab-server-*.tar.gz
cd auto-grab-server-*

# 2. 一键部署
./deploy.sh
```

**Windows服务器**：
```cmd
# 1. 上传文件夹到服务器
# 2. 运行部署脚本
deploy.bat
```

### 方式2: Docker部署

```bash
# 上传并解压后
cd auto-grab-server-*
./deploy-docker.sh
```

### 方式3: 手动部署

```bash
# 1. 安装Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 安装依赖
npm ci --only=production

# 3. 启动服务
node index.js
```

## 🔧 配置说明

### 端口配置
- 默认端口：3000
- 修改端口：编辑 `ecosystem.config.js` 或设置环境变量 `PORT=8080`

### 反向代理（可选）
如果需要使用域名访问，可以配置Nginx：
```bash
sudo cp nginx.conf /etc/nginx/sites-available/auto-grab-server
sudo ln -s /etc/nginx/sites-available/auto-grab-server /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

## 📊 验证部署

部署完成后，访问以下地址验证：

- **健康检查**: `http://your-server:3000/health`
- **服务状态**: `http://your-server:3000/auto-grab-status`

## 🎯 API使用

### 启动自动抢单
```bash
curl -X POST http://your-server:3000/start-auto-grab \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "12345",
    "token": "your-token",
    "interval": 5000,
    "allowedAreas": ["QQ"]
  }'
```

### 查看运行状态
```bash
curl http://your-server:3000/auto-grab-status
```

### 停止自动抢单
```bash
curl -X POST http://your-server:3000/stop-auto-grab
```

## 🔍 常用命令

### PM2管理
```bash
pm2 status                    # 查看状态
pm2 logs auto-grab-server     # 查看日志
pm2 restart auto-grab-server  # 重启服务
pm2 stop auto-grab-server     # 停止服务
```

### Docker管理
```bash
docker-compose ps             # 查看状态
docker-compose logs -f        # 查看日志
docker-compose restart        # 重启服务
docker-compose down           # 停止服务
```

### 系统服务管理
```bash
sudo systemctl status auto-grab-server   # 查看状态
sudo systemctl restart auto-grab-server  # 重启服务
sudo journalctl -u auto-grab-server -f   # 查看日志
```

## 🚨 故障排除

### 1. 端口被占用
```bash
sudo lsof -i :3000
sudo kill -9 <PID>
```

### 2. 权限问题
```bash
sudo chown -R $USER:$USER /path/to/auto-grab-server
```

### 3. 防火墙问题
```bash
# Ubuntu/Debian
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 4. 内存不足
```bash
# 检查内存
free -h

# 添加swap（如果需要）
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 🔒 安全建议

1. **使用反向代理**：通过Nginx等代理服务器访问
2. **配置防火墙**：只开放必要的端口
3. **定期更新**：保持系统和依赖包更新
4. **监控日志**：定期检查应用日志
5. **备份数据**：定期备份重要配置

## 📈 性能优化

1. **使用PM2集群模式**：
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'auto-grab-server',
    script: 'index.js',
    instances: 'max',
    exec_mode: 'cluster'
  }]
};
```

2. **配置日志轮转**：
```bash
sudo nano /etc/logrotate.d/auto-grab-server
```

3. **监控资源使用**：
```bash
htop
pm2 monit
```

## 📞 技术支持

如果遇到部署问题：

1. 检查 `logs/` 目录下的日志文件
2. 确认Node.js版本 >= 16
3. 验证网络连接和防火墙设置
4. 查看系统资源使用情况

部署成功后，您的自动抢单服务器就可以通过API接口提供服务了！
