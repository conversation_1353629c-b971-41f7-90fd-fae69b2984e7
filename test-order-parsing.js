const fs = require('fs');
const path = require('path');

// 读取示例JSON文件
function loadTestData() {
  try {
    const jsonPath = path.join(__dirname, '0000000.json');
    const jsonData = fs.readFileSync(jsonPath, 'utf8');
    return JSON.parse(jsonData);
  } catch (error) {
    console.error('读取测试数据失败:', error.message);
    return null;
  }
}

// 模拟订单解析逻辑
function parseOrderResponse(orderResponse) {
  console.log('🔍 开始解析订单响应...');
  console.log('原始响应:', JSON.stringify(orderResponse, null, 2));
  
  // 检查响应结构
  console.log('\n📊 响应结构分析:');
  console.log('- code:', orderResponse?.code);
  console.log('- message:', orderResponse?.message);
  console.log('- msg:', orderResponse?.msg);
  console.log('- count:', orderResponse?.count);
  console.log('- data类型:', typeof orderResponse?.data);
  console.log('- data是否为数组:', Array.isArray(orderResponse?.data));
  
  // 检查是否成功获取到订单数据 (实际API返回code: 0表示成功)
  if (orderResponse && orderResponse.code === 0 && orderResponse.data) {
    const orders = Array.isArray(orderResponse.data) ? orderResponse.data : [];
    
    console.log(`\n🎯 解析结果: 发现 ${orders.length} 个订单`);
    
    if (orders.length > 0) {
      console.log('\n📋 订单详情:');
      orders.forEach((order, index) => {
        console.log(`订单 ${index + 1}:`);
        console.log(`  - ID: ${order.id}`);
        console.log(`  - 标题: ${order.gg_title}`);
        console.log(`  - 游戏区域: ${order.game_area}`);
        console.log(`  - 更新时间: ${order.update_time}`);
        console.log(`  - 商品ID: ${order.goods_id}`);
        console.log(`  - 分类: ${order.classify}`);
        console.log(`  - 备注A: ${order.pay_texta}`);
        console.log(`  - 备注B: ${order.pay_textb}`);
        console.log(`  - 系统文本: ${order.sytext}`);
        console.log('');
      });
      
      return {
        success: true,
        orderCount: orders.length,
        orders: orders
      };
    } else {
      console.log('\n😴 暂无可抢订单');
      return {
        success: true,
        orderCount: 0,
        orders: []
      };
    }
  } else {
    console.log('\n⚠️ 获取订单列表失败或无数据');
    console.log('失败原因分析:');
    console.log('- code不等于0:', orderResponse?.code !== 0);
    console.log('- data为空:', !orderResponse?.data);
    
    return {
      success: false,
      error: '获取订单失败',
      code: orderResponse?.code,
      message: orderResponse?.msg || orderResponse?.message
    };
  }
}

// 模拟抢单逻辑测试
function testGrabLogic(orders) {
  console.log('\n🎯 测试抢单逻辑...');
  
  for (const order of orders) {
    const orderId = order.id;
    if (!orderId) {
      console.log('⚠️ 订单ID为空，跳过', order);
      continue;
    }
    
    console.log(`\n🎯 准备抢单: ${orderId}`);
    console.log(`  - 订单标题: ${order.gg_title}`);
    console.log(`  - 游戏区域: ${order.game_area}`);
    console.log(`  - 商品ID: ${order.goods_id}`);
    
    // 这里会调用实际的抢单API
    console.log(`  ⚡ 模拟抢单请求: grabOrder("${orderId}", token)`);
  }
}

// 主测试函数
function main() {
  console.log('🧪 开始测试订单解析逻辑\n');
  
  // 加载测试数据
  const testData = loadTestData();
  if (!testData) {
    console.error('❌ 无法加载测试数据');
    return;
  }
  
  // 解析订单响应
  const parseResult = parseOrderResponse(testData);
  
  // 测试抢单逻辑
  if (parseResult.success && parseResult.orders.length > 0) {
    testGrabLogic(parseResult.orders);
  }
  
  console.log('\n✅ 测试完成');
  console.log('解析结果:', parseResult);
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  parseOrderResponse,
  testGrabLogic
};
